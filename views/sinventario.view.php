<?php
#region region DOCS

/** @var Contenedor[] $contenedores */
/** @var Activo[] $activos */
/** @var string $error_text */
/** @var string $error_display */

use App\classes\Contenedor;
use App\classes\Activo;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Sacar de contenedor</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Sacar de contenedor</h4>
				<p class="mb-0 text-muted">Sacar unidades de un registro de inventario existente</p>
			</div>
			<div class="ms-auto">
				<a href="linventario" class="btn btn-secondary"><i class="fa fa-arrow-left fa-fw me-1"></i> Volver al listado</a>
			</div>
		</div>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region ALERT ERROR ?>
		<?php if ($error_display === 'show'): ?>
			<div class="alert alert-danger alert-dismissible fade show">
				<strong>Error!</strong> <?php echo $error_text; ?>
				<button type="button" class="btn-close" data-bs-dismiss="alert"></button>
			</div>
		<?php endif; ?>
		<?php #endregion ALERT ERROR ?>
		
		<?php #region region FORM PANEL ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">Formulario de Resta de Inventario</h4>
			</div>
			<div class="panel-body">
				<form action="sinventario" method="POST" id="inventario-form">
					<div class="row">
						<div class="col-md-6">
							<div class="mb-3">
								<label for="id_contenedor" class="form-label">Contenedor: <span class="text-danger">*</span></label>
								<select class="form-select" id="id_contenedor" name="id_contenedor" required>
									<option value="">-- Seleccione un contenedor --</option>
									<?php foreach ($contenedores as $contenedor): ?>
										<option value="<?php echo $contenedor->getId(); ?>">
											<?php echo htmlspecialchars($contenedor->getDescripcion()); ?>
										</option>
									<?php endforeach; ?>
								</select>
								<div class="invalid-feedback">Por favor seleccione un contenedor.</div>
							</div>
						</div>
					</div>
					
					<div id="activos-container" class="d-none">
						<div class="row">
							<div class="col-12">
								<div class="alert alert-info">
									<i class="fa fa-info-circle me-2"></i> Seleccione los activos que desea sacar del contenedor
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-12">
								<div class="card">
									<div class="card-header d-flex align-items-center">
										<h5 class="mb-0">Activos disponibles en el contenedor <span class="text-danger">*</span></h5>
										<div class="ms-auto">
											<div class="form-check">
												<input class="form-check-input" type="checkbox" id="select-all-activos">
												<label class="form-check-label" for="select-all-activos">
													Seleccionar todos
												</label>
											</div>
										</div>
									</div>
									<div class="card-body p-3">
										<input type="text" class="form-control form-control-sm mb-3" id="search-activos" placeholder="Buscar activos...">
										<div id="activos-list" class="row">
											<!-- Los activos se cargarán aquí mediante AJAX -->
											<div class="col-12 text-center py-4" id="loading-activos" style="display: none;">
												<div class="spinner-border text-primary" role="status">
													<span class="visually-hidden">Cargando...</span>
												</div>
												<p class="mt-2">Cargando activos...</p>
											</div>
											<div class="col-12 text-center py-4" id="no-activos" style="display: none;">
												<p class="text-muted">No hay activos disponibles en este contenedor</p>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row mt-3">
						<div class="col-md-12">
							<button type="submit" id="btn-sacar" class="btn btn-danger" disabled>
								<i class="fa fa-minus fa-fw me-1"></i> Sacar
							</button>
							<a href="linventario" class="btn btn-secondary ms-2">
								<i class="fa fa-times fa-fw me-1"></i> Cancelar
							</a>
						</div>
					</div>
				</form>
			</div>
		</div>
		<?php #endregion FORM PANEL ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Elements
        const form = document.getElementById('inventario-form');
        const contenedorSelect = document.getElementById('id_contenedor');
        const activosContainer = document.getElementById('activos-container');
        const activosList = document.getElementById('activos-list');
        const loadingActivos = document.getElementById('loading-activos');
        const noActivos = document.getElementById('no-activos');
        const btnSacar = document.getElementById('btn-sacar');
        const selectAllCheckbox = document.getElementById('select-all-activos');
        const searchInput = document.getElementById('search-activos');
        
        // Initialize select2 for better dropdown experience (if available)
        if (typeof $.fn.select2 !== 'undefined') {
            $('#id_contenedor').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });
        }
        
        // Form validation
        if (form) {
            form.addEventListener('submit', function (event) {
                // Check if at least one activo is selected
                const selectedActivos = document.querySelectorAll('input[name="activos[]"]:checked');
                
                if (selectedActivos.length === 0) {
                    event.preventDefault();
                    event.stopPropagation();
                    alert('Por favor, seleccione al menos un activo para sacar del inventario.');
                    return false;
                }
                
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                
                form.classList.add('was-validated');
            });
        }
        
        // Load activos when contenedor changes
        if (contenedorSelect) {
            contenedorSelect.addEventListener('change', function() {
                const contenedorId = this.value;
                
                // Reset activos section
                activosList.innerHTML = '';
                btnSacar.disabled = true;
                
                if (contenedorId) {
                    // Show activos container
                    activosContainer.classList.remove('d-none');
                    
                    // Show loading
                    loadingActivos.style.display = 'block';
                    noActivos.style.display = 'none';
                    
                    // Fetch activos for this contenedor
                    fetch(`get_activos_by_contenedor?id_contenedor=${contenedorId}`, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Hide loading
                        loadingActivos.style.display = 'none';
                        
                        if (data.success && data.activos && data.activos.length > 0) {
                            // Create activos checkboxes
                            data.activos.forEach(activo => {
                                const col = document.createElement('div');
                                col.className = 'col-md-6 col-lg-4 mb-3';
                                
                                const card = document.createElement('div');
                                card.className = 'card h-100';
                                
                                const cardBody = document.createElement('div');
                                cardBody.className = 'card-body';
                                
                                const checkboxDiv = document.createElement('div');
                                checkboxDiv.className = 'form-check';
                                
                                const checkbox = document.createElement('input');
                                checkbox.type = 'checkbox';
                                checkbox.className = 'form-check-input activo-checkbox';
                                checkbox.name = 'activos[]';
                                checkbox.value = activo.id;
                                checkbox.id = `activo-${activo.id}`;
                                checkbox.addEventListener('change', updateSacarButton);
                                
                                const label = document.createElement('label');
                                label.className = 'form-check-label';
                                label.htmlFor = `activo-${activo.id}`;
                                label.innerHTML = `<strong>${activo.descripcion}</strong>`;
                                
                                const marca = document.createElement('p');
                                marca.className = 'text-muted mb-0 mt-2';
                                marca.textContent = `Marca: ${activo.marca}`;
                                
                                checkboxDiv.appendChild(checkbox);
                                checkboxDiv.appendChild(label);
                                
                                cardBody.appendChild(checkboxDiv);
                                cardBody.appendChild(marca);
                                
                                card.appendChild(cardBody);
                                col.appendChild(card);
                                
                                activosList.appendChild(col);
                            });
                            
                            // Enable select all checkbox
                            selectAllCheckbox.disabled = false;
                        } else {
                            // Show no activos message
                            noActivos.style.display = 'block';
                            
                            // Disable select all checkbox
                            selectAllCheckbox.disabled = true;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        loadingActivos.style.display = 'none';
                        noActivos.style.display = 'block';
                        noActivos.textContent = 'Error al cargar los activos. Por favor, inténtelo de nuevo.';
                    });
                } else {
                    // Hide activos container if no contenedor selected
                    activosContainer.classList.add('d-none');
                }
            });
        }
        
        // Select all activos
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.activo-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                
                updateSacarButton();
            });
        }
        
        // Search functionality
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const activoCards = document.querySelectorAll('#activos-list .col-md-6, #activos-list .col-lg-4');
                
                activoCards.forEach(card => {
                    const label = card.querySelector('.form-check-label');
                    const marca = card.querySelector('.text-muted');
                    
                    if (label) {
                        const labelText = label.textContent.toLowerCase();
                        const marcaText = marca ? marca.textContent.toLowerCase() : '';
                        
                        if (labelText.includes(searchTerm) || marcaText.includes(searchTerm)) {
                            card.style.display = '';
                        } else {
                            card.style.display = 'none';
                        }
                    }
                });
            });
        }
        
        // Function to update Sacar button state
        function updateSacarButton() {
            const selectedActivos = document.querySelectorAll('input[name="activos[]"]:checked');
            btnSacar.disabled = selectedActivos.length === 0;
        }
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
