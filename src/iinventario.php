<?php

// Iniciar sesión si es necesario
use App\classes\Inventario;
use App\classes\InventarioMovimiento;
use App\classes\Contenedor;
use App\classes\Activo;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en iinventario.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$contenedores         = [];      // Initialize as an empty array
$activos_no_asociados = [];      // Initialize as an empty array
$error_text           = '';
$error_display        = 'none';
$selected_contenedor  = null;    // Store the selected contenedor ID for form persistence
#endregion init variables

#region region Handle POST Request
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate and sanitize input
    $id_contenedor         = filter_input(INPUT_POST, 'id_contenedor', FILTER_VALIDATE_INT);
    $activos_seleccionados = isset($_POST['activos']) ? $_POST['activos'] : [];

    // Store the selected contenedor ID for form persistence
    $selected_contenedor = $id_contenedor;

    // Handle optional redirect URL (allowlist only scanactivo?id=NUM)
    $redirect_url = null;
    $redirect_raw = filter_input(INPUT_POST, 'redirect_url', FILTER_UNSAFE_RAW);
    if ($redirect_raw && preg_match('/^scanactivo\?id=([0-9]+)$/', $redirect_raw)) {
        $redirect_url = $redirect_raw;
    }
    
    // Validate required fields
    if (!$id_contenedor || empty($activos_seleccionados)) {
        $error_display = 'show';
        $error_text = "Debe seleccionar un contenedor y al menos un activo.";
    } else {
        try {
            $activos_asociados = 0;
            $errores = 0;
            
            // Begin transaction
            $conexion->beginTransaction();
            
            foreach ($activos_seleccionados as $id_activo) {
                $id_activo = filter_var($id_activo, FILTER_VALIDATE_INT);
                
                if (!$id_activo) {
                    continue; // Skip invalid activo IDs
                }
                
                // Check if inventory already exists for this contenedor and activo
                $existingInventario = Inventario::get_by_contenedor_activo($id_contenedor, $id_activo, $conexion);
                
                if ($existingInventario) {
                    // If inventory already exists, skip this activo
                    continue;
                }
                
                // Create new inventory with quantity 1
                $inventario = new Inventario();
                $inventario->setId_contenedor($id_contenedor);
                $inventario->setId_activo($id_activo);
                $inventario->setCantidad(1); // Fixed quantity of 1
                
                $result = $inventario->crear($conexion);
                
                if ($result) {
                    // Create a movement record
                    try {
                        $movimiento = new InventarioMovimiento();
                        $movimiento->setId_contenedor($id_contenedor);
                        $movimiento->setId_activo($id_activo);
                        $movimiento->setCantidad(1); // Fixed quantity of 1
                        $movimiento->setId_usuario($_SESSION[USR_SESSION]);
                        $movimiento->setTipo_movimiento('entrada');
                        
                        $movimiento->crear($conexion);
                        $activos_asociados++;
                    } catch (Exception $e) {
                        error_log("Error al crear movimiento de inventario para activo ID $id_activo: " . $e->getMessage());
                        $errores++;
                    }
                } else {
                    $errores++;
                }
            }
            
            // Commit transaction
            $conexion->commit();
            
            if ($activos_asociados > 0) {
                // Success - redirect based on redirect_url parameter or default to inventory list
                $final_redirect = $redirect_url ?: 'linventario';

                if ($errores > 0) {
                    $_SESSION['flash_message_success'] = "Se asociaron $activos_asociados activos al contenedor, pero hubo $errores errores.";
                } else {
                    $_SESSION['flash_message_success'] = "Se asociaron $activos_asociados activos al contenedor correctamente.";
                }
                header("Location: $final_redirect");
                exit;
            } else {
                $error_display = 'show';
                $error_text = "No se pudo asociar ningún activo al contenedor. Por favor, inténtelo de nuevo.";
            }
            
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($conexion->inTransaction()) {
                $conexion->rollBack();
            }
            
            $error_display = 'show';
            $error_text = "Error: " . $e->getMessage();
        }
    }
}
#endregion Handle POST Request

#region try
try {
    // Get list of contenedores for dropdown
    $contenedores = Contenedor::get_list($conexion);
    
    // Get list of activos that are not associated with any contenedor
    $activos_no_asociados = Inventario::get_activos_no_asociados($conexion);
    
} catch (PDOException $e) {
    // Specific handling for database errors
    error_log("Database error fetching data: " . $e->getMessage());
    $error_display = 'show';
    $error_text = "Error de base de datos al obtener la lista de contenedores o activos.";
} catch (Exception $e) {
    // General error handling
    error_log("Error fetching data: " . $e->getMessage());
    $error_display = 'show';
    $error_text = "Ocurrió un error inesperado: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/iinventario.view.php';

?>
