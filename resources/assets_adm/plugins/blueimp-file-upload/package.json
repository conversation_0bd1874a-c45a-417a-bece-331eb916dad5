{"name": "blueimp-file-upload", "version": "10.32.0", "title": "jQuery File Upload", "description": "File Upload widget with multiple file selection, drag&drop support, progress bar, validation and preview images, audio and video for jQuery. Supports cross-domain, chunked and resumable file uploads. Works with any server-side platform (Google App Engine, PHP, Python, Ruby on Rails, Java, etc.) that supports standard HTML form file uploads.", "keywords": ["j<PERSON>y", "file", "upload", "widget", "multiple", "selection", "drag", "drop", "progress", "preview", "cross-domain", "cross-site", "chunk", "resume", "gae", "go", "python", "php", "bootstrap"], "homepage": "https://github.com/blueimp/jQuery-File-Upload", "author": {"name": "<PERSON>", "url": "https://blueimp.net"}, "repository": {"type": "git", "url": "git://github.com/blueimp/jQuery-File-Upload.git"}, "license": "MIT", "peerDependencies": {"jquery": ">=1.7"}, "optionalDependencies": {"blueimp-canvas-to-blob": "3", "blueimp-load-image": "5", "blueimp-tmpl": "3"}, "devDependencies": {"eslint": "7", "eslint-config-blueimp": "2", "eslint-config-prettier": "8", "eslint-plugin-jsdoc": "36", "eslint-plugin-prettier": "4", "prettier": "2", "stylelint": "13", "stylelint-config-prettier": "8", "stylelint-config-recommended": "5"}, "stylelint": {"extends": ["stylelint-config-recommended", "stylelint-config-prettier"], "ignoreFiles": ["css/*.min.css", "css/vendor/*", "test/vendor/*"]}, "eslintConfig": {"extends": ["blueimp", "plugin:jsdoc/recommended", "plugin:prettier/recommended"], "env": {"browser": true}}, "eslintIgnore": ["js/*.min.js", "test/vendor"], "prettier": {"arrowParens": "avoid", "proseWrap": "always", "singleQuote": true, "trailingComma": "none"}, "scripts": {"lint": "stylelint '**/*.css' && eslint .", "unit": "docker-compose run --rm mocha", "wdio": "docker-compose run --rm wdio", "test": "npm run lint && npm run unit && npm run wdio && npm run wdio -- conf/firefox.js", "posttest": "docker-compose down -v", "preversion": "npm test", "postversion": "git push --tags origin master && npm publish"}, "files": ["css/jquery.fileupload-noscript.css", "css/jquery.fileupload-ui-noscript.css", "css/jquery.fileupload-ui.css", "css/jquery.fileupload.css", "img/loading.gif", "img/progressbar.gif", "js/cors/jquery.postmessage-transport.js", "js/cors/jquery.xdr-transport.js", "js/vendor/jquery.ui.widget.js", "js/jquery.fileupload-audio.js", "js/jquery.fileupload-image.js", "js/jquery.fileupload-process.js", "js/jquery.fileupload-ui.js", "js/jquery.fileupload-validate.js", "js/jquery.fileupload-video.js", "js/jquery.fileupload.js", "js/jquery.iframe-transport.js"], "main": "js/jquery.fileupload.js"}